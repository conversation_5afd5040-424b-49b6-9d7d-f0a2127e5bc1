'use client';

import { useState } from 'react';
import SuccessAlert from './components/ui/SuccessAlert';
import { Title, Container, Text, ThemeIcon, Group, Button, Paper, Stack, SimpleGrid, Card } from "@mantine/core";
import { IoDocumentTextSharp, IoTimerSharp, IoSparklesSharp, IoPricetagsSharp, IoSearchSharp, IoChatbubbleEllipsesSharp, IoRestaurantSharp, IoHeadsetSharp, IoConstructSharp, IoStarSharp, IoShareSocialSharp, IoCreateSharp, IoSchoolSharp } from "react-icons/io5";
import { MdFormatColorText } from "react-icons/md";
import cardClasses from "./theme/Card.module.css";

import heroClasses from "./theme/Hero.module.css";
import { Balancer } from "react-wrap-balancer";
import Link from "next/link";
import { HeaderMenu } from "./components/ui/HeaderMenu";
import { ChatCreationModal } from "./components/ui/ChatCreationModal";

export default function HomePage() {
  const [chatModalOpened, setChatModalOpened] = useState(false);

  return (
    <Container size="full" p={0}>
      <HeaderMenu />

      <SuccessAlert />
      
      {/* Hero Section */}
      <Container size="lg" px={32} className={heroClasses.root}>
          <Stack flex={1} w="100%" className={heroClasses.content}>
            <Title order={1} className={heroClasses.title}>
              <Balancer>Make sense of video content in seconds</Balancer>
            </Title>
            <Text size="xl" ta="center" mx="auto">
              <Balancer>
                Reclaim your time and attention while finding the content you really need. 
                Perfect for the non-neurotypical, chronically busy, and everyone who wants to do more in less time.
              </Balancer>
            </Text>

            <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="lg" mt="xl">
              <Card h="100%" p="md" shadow="md" radius="0">
                  <Card.Section bg="var(--mantine-color-black)" ml="-md" mr="-md" style={{ aspectRatio: 1.5 }} className={cardClasses.cardSection}>
                    <MdFormatColorText size={100} color="var(--mantine-color-white)" />
                </Card.Section>
                <Card.Section p="lg">
                    <Text size="lg">
                      Convert any YouTube video into useful, editable text articles and ask follow-up questions.
                    </Text>
                </Card.Section>
              </Card>
              
              <Card h="100%" p="md" shadow="md" radius="0">
                  <Card.Section bg="var(--mantine-color-black)" ml="-md" mr="-md" style={{ aspectRatio: 1.5 }} className={cardClasses.cardSection}>
                    <IoTimerSharp size={100} color="var(--mantine-color-white)" />
                </Card.Section>
                <Card.Section p="lg">
                    <Text size="lg">
                      Minutes instead of hours to extract useful parts from long-form video content.
                    </Text>
                </Card.Section>
              </Card>
              
              <Card h="100%" p="md" shadow="md" radius="0">
                  <Card.Section bg="var(--mantine-color-black)" ml="-md" mr="-md" style={{ aspectRatio: 1.5 }} className={cardClasses.cardSection}>
                    <IoSparklesSharp size={100} color="var(--mantine-color-white)" />
                </Card.Section>
                <Card.Section p="lg">
                    <Text size="lg">
                      Perfect for ADHD minds and busy professionals who need content without distraction.
                    </Text>
                </Card.Section>
              </Card>
            </SimpleGrid>

            <Group mt={40} justify="center">
              <Button radius="xl" custom-color="success" size="lg" className={heroClasses.control} component={Link} href="/auth#signup">
                Get started for free
              </Button>
            </Group>
          </Stack>

      </Container>

      {/* Features Section */}
      <Container size="full" p={0} className={heroClasses.root} bg="gray.1" py="xl">
        <Container size="md" p={32}>
          <Title order={2} ta="center" mb="xl">Powerful Features</Title>
          <SimpleGrid cols={{ base: 1, sm: 2, lg: 3 }} spacing="lg">
            <Card p="lg" radius="0" shadow="sm" className={cardClasses.featureCard}>
              <Card.Section>
                  
            <Group>
                    <IoDocumentTextSharp size={30} /><IoDocumentTextSharp size={30} /><IoDocumentTextSharp size={30} />
                    </Group>
                    </Card.Section>
                    <Card.Section>

                  <Title className={cardClasses.featureTitle} order={4}>Auto Conversion</Title>
                  <Text>
                    Automatically convert any video with spoken content into useful pieces of text.
                  </Text>
              
              </Card.Section>
            </Card>
            
            <Card p="lg" radius="0" shadow="sm">
              <Card.Section>
                <Stack gap="sm" align="center">
                  <ThemeIcon size={60} radius={0} color="green">
                    <IoSearchSharp size={30} />
                  </ThemeIcon>
                  <Title order={4} ta="center">Smart Navigation</Title>
                  <Text ta="center" size="sm">
                    Find relevant parts easily with timestamps and backlinks to video sections.
                  </Text>
                </Stack>
              </Card.Section>
            </Card>
            
            <Card p="lg" radius="0" shadow="sm">
              <Card.Section>
                <Stack gap="sm" align="center">
                  <ThemeIcon size={60} radius={0} color="violet">
                    <IoChatbubbleEllipsesSharp size={30} />
                  </ThemeIcon>
                  <Title order={4} ta="center">AI Follow-up</Title>
                  <Text ta="center" size="sm">
                    Ask follow-up questions about the video and edit the text with AI assistance.
                  </Text>
                </Stack>
              </Card.Section>
            </Card>
            
            <Card p="lg" radius="0" shadow="sm">
              <Card.Section>
                <Stack gap="sm" align="center">
                  <ThemeIcon size={60} radius={0} color="orange">
                    <IoPricetagsSharp size={30} />
                  </ThemeIcon>
                  <Title order={4} ta="center">Smart Organization</Title>
                  <Text ta="center" size="sm">
                    Create content collections through our robust tagging system and comparisons.
                  </Text>
                </Stack>
              </Card.Section>
            </Card>

            <Card p="lg" radius="0" shadow="sm">
              <Card.Section>
                <Stack gap="sm" align="center">
                  <ThemeIcon size={60} radius={0} color="blue">
                    <IoShareSocialSharp size={30} />
                  </ThemeIcon>
                  <Title order={4} ta="center">Easy Sharing</Title>
                  <Text ta="center" size="sm">
                    Share your converted content and insights with colleagues, friends, or your team.
                  </Text>
                </Stack>
              </Card.Section>
            </Card>

            <Card p="lg" radius="0" shadow="sm">
              <Card.Section>
                <Stack gap="sm" align="center">
                  <ThemeIcon size={60} radius={0} color="teal">
                    <IoDocumentTextSharp size={30} />
                  </ThemeIcon>
                  <Title order={4} ta="center">AI-Enhanced Transcripts</Title>
                  <Text ta="center" size="sm">
                    Get full, intelligently formatted transcripts with speaker identification and context.
                  </Text>
                </Stack>
              </Card.Section>
            </Card>

            <Card p="lg" radius="0" shadow="sm">
              <Card.Section>
                <Stack gap="sm" align="center">
                  <ThemeIcon size={60} radius={0} color="pink">
                    <IoCreateSharp size={30} />
                  </ThemeIcon>
                  <Title order={4} ta="center">AI-Supported Editing</Title>
                  <Text ta="center" size="sm">
                    Edit and refine your content with AI assistance for better clarity and structure.
                  </Text>
                </Stack>
              </Card.Section>
            </Card>
          </SimpleGrid>
        </Container>
      </Container>

      {/* Use Cases Section */}
      <Container size="full" p={0}>
        <Container size="md" px={32}>
          <Title order={2} ta="center" mb="xl">Perfect For Every Need</Title>
          <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="xl">
            <Paper p="xl" radius="lg" shadow="md" withBorder>
              <Stack gap="md">
                <Group gap="sm" align="center">
                  <IoStarSharp size={32} color="#FF9800" />
                  <Title order={3}>Product Reviews</Title>
                </Group>
                <Text>
                  Compare different takes on the same product, or reviews of different products – 
                  without manually creating lists of pros and cons. Perfect for informed purchasing decisions.
                </Text>
              </Stack>
            </Paper>
            
            <Paper p="xl" radius="lg" shadow="md" withBorder>
              <Stack gap="md">
                <Group gap="sm" align="center">
                  <IoRestaurantSharp size={32} color="#4CAF50" />
                  <Title order={3}>Cooking Recipes</Title>
                </Group>
                <Text>
                  Turn any cooking video into an easy-to-follow recipe with ingredient lists, 
                  steps, and useful tips – all at a glance without rewinding.
                </Text>
              </Stack>
            </Paper>
            
            <Paper p="xl" radius="lg" shadow="md" withBorder>
              <Stack gap="md">
                <Group gap="sm" align="center">
                  <IoHeadsetSharp size={32} color="#9C27B0" />
                  <Title order={3}>Video Podcasts</Title>
                </Group>
                <Text>
                  Love long podcasts but too busy to watch? Get timestamped summaries 
                  that help you find the highlights that matter to you.
                </Text>
              </Stack>
            </Paper>
            
            <Paper p="xl" radius="lg" shadow="md" withBorder>
              <Stack gap="md">
                <Group gap="sm" align="center">
                  <IoConstructSharp size={32} color="#2196F3" />
                  <Title order={3}>Tutorials</Title>
                </Group>
                <Text>
                  From tech tutorials to DIY guides: Create step-by-step instructions 
                  that let you work hands-free or jump to relevant video sections.
                </Text>
              </Stack>
            </Paper>

            <Paper p="xl" radius="lg" shadow="md" withBorder>
              <Stack gap="md">
                <Group gap="sm" align="center">
                  <IoSchoolSharp size={32} color="#E91E63" />
                  <Title order={3}>Research</Title>
                </Group>
                <Text>
                  Transform educational videos, lectures, and documentaries into searchable, 
                  citable content. Perfect for students, academics, and professionals conducting research.
                </Text>
              </Stack>
            </Paper>
          </SimpleGrid>
        </Container>
      </Container>

      {/* CTA Section */}
      <Container size="full" p={0} className={heroClasses.root} bg="dark" py="xl">
        <Container size="md" px={32}>
          <Stack align="center" gap="lg">
            <Title order={2} c="white" ta="center">
              <Balancer>Ready to reclaim your time and attention?</Balancer>
            </Title>
            <Text size="lg" c="gray.3" ta="center" maw={500}>
              Join thousands who&apos;ve already discovered the power of distraction-free content consumption.
            </Text>
            <Group gap="md">
              <Button component={Link} href="/auth#signup">
                Start for free
              </Button>
              <Button onClick={() => setChatModalOpened(true)}>
                See it in action
              </Button>
            </Group>
          </Stack>
        </Container>
      </Container>

      <ChatCreationModal
        opened={chatModalOpened}
        onClose={() => setChatModalOpened(false)}
      />
    </Container>
  );
}
