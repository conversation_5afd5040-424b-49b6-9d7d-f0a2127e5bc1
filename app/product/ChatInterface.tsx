'use client';

import { useState, useCallback, useRef, useEffect } from "react";
import { Box, Group, Text, ActionIcon } from '@mantine/core';
import { IconX } from '@tabler/icons-react';
import SavedChats from "./SavedChats";
import { NodeStatus } from "./components/dify/NodeStatusDisplay";
import { DifyStreamProcessor } from "./components/dify/DifyStreamProcessor";
import { ConversationProgress } from "./components/ui/ThreeStepProgress";

import { Message } from "./lib/types";
import { VideoMetadata } from "./lib/videoTypes";

import SummaryTranscriptView from "./components/layout/SummaryTranscriptView";
import VideoMetadataCard from "./components/ui/VideoMetadataCard";
import ChatWindow from "./components/layout/ChatWindow";
import InterfaceClasses from "./theme/Interface.module.css";
import { useUserData } from '@/contexts/UserDataContext';
import { useConversationCreation } from '@/app/hooks/useConversationCreation';
import { useConversationProgress } from '@/app/hooks/useConversationProgress';

interface ChatInterfaceProps {
	initialConversationId?: string | null;
}

interface ConversationMessage {
  id: string;
  content: string;
  message_number: number;
  created_at: string;
  user_message: boolean;
}

interface ConversationListItem {
  id: string;
  name: string;
}

export default function ChatInterface({ initialConversationId }: ChatInterfaceProps) {
	const chatContainerRef = useRef<HTMLDivElement>(null);
	const [messages, setMessages] = useState<Message[]>([]);
	const [loading, setLoading] = useState(false);
	const [inputValue, setInputValue] = useState('');
	const [streamInput, setStreamInput] = useState('');
	const [conversationId, setConversationId] = useState<string | null>(null);
	const [nodeStatus, setNodeStatus] = useState<NodeStatus>({
		currentNode: '',
		currentNodeIndex: 0,
		totalNodes: 0,
		workflowRunId: null,
		status: 'none'
	});
	const [summaryContent, setSummaryContent] = useState<string | null>(null);
	const [refreshTrigger, setRefreshTrigger] = useState(0);
	const [currentChatTitle, setCurrentChatTitle] = useState<string>("Processing…");
	const [validationError, setValidationError] = useState<string | null>(null);
	const [showNewChatInput, setShowNewChatInput] = useState(false);
	const [currentVideoUrl, setCurrentVideoUrl] = useState<string | null>(null);
	const [videoMetadata, setVideoMetadata] = useState<VideoMetadata | null>(null);
	const [creatingConversation, setCreatingConversation] = useState(false);
	const [pendingConversations, setPendingConversations] = useState<Set<string>>(new Set());
	const [chatMinimized, setChatMinimized] = useState(true);
	const [isPinned, setIsPinned] = useState(false);
	const [rightPanelExpanded, setRightPanelExpanded] = useState(false);
	const [sidebarManualOverride, setSidebarManualOverride] = useState<'collapsed' | 'expanded' | null>(null);
	const [videoSeekFunction, setVideoSeekFunction] = useState<((seconds: number) => void) | null>(null);
	const [transcriptRefreshTrigger, setTranscriptRefreshTrigger] = useState(0);
	const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const [conversationProgress, setConversationProgress] = useState<ConversationProgress | null>(null);
	const [titleAnimations, setTitleAnimations] = useState<Map<string, { oldTitle: string; newTitle: string }>>(new Map());
	const titleTimeoutRefs = useRef<Map<string, NodeJS.Timeout>>(new Map());
	const { user } = useUserData();

	// Use the new hooks
	const { createConversationBackground } = useConversationCreation();
	const { subscribeToProgress, checkExistingProcessing: checkExistingProcessingHook } = useConversationProgress();

	const handleTitleUpdate = useCallback((conversationId: string, oldTitle: string, newTitle: string) => {
		// Check if animation is already running for this exact title change
		const existingAnimation = titleAnimations.get(conversationId);
		if (existingAnimation && existingAnimation.newTitle === newTitle) {
			// Same title change is already animating, don't restart
			return;
		}

		// Clear any existing timeout for this conversation
		const existingTimeout = titleTimeoutRefs.current.get(conversationId);
		if (existingTimeout) {
			clearTimeout(existingTimeout);
		}

		// Update title and start animation
		setCurrentChatTitle(newTitle);
		setTitleAnimations(prev => new Map(prev).set(conversationId, { oldTitle, newTitle }));
		
		// Set new timeout to clear animation
		const newTimeout = setTimeout(() => {
			setTitleAnimations(prev => {
				const newMap = new Map(prev);
				newMap.delete(conversationId);
				return newMap;
			});
			titleTimeoutRefs.current.delete(conversationId);
		}, 2000);
		
		titleTimeoutRefs.current.set(conversationId, newTimeout);
	}, [titleAnimations]);


	const sendMessage = async () => {
		if (!inputValue.trim()) return;
		
		setValidationError(null);
		
		if (!conversationId) {
			setValidationError('Please start a new conversation first');
			return;
		}
		
		const currentInput = inputValue;
		const userMessageId = Date.now().toString();
		const loadingMessageId = `loading-${Date.now()}`;
		
		const userMessage: Message = {
			id: userMessageId,
			type: 'user',
			content: currentInput
		};
		
		const loadingMessage: Message = {
			id: loadingMessageId,
			type: 'ai',
			content: 'loading'
		};
		
		setMessages(prev => [...prev, userMessage, loadingMessage]);
		setInputValue('');
		setLoading(true);
		
		try {
			const response = await fetch('/api/product/submit_message', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ conversationId, message: currentInput })
			});
			
			const result = await response.json();
			
			if (!response.ok) {
				setMessages(prev => prev.filter(msg => msg.id !== userMessageId && msg.id !== loadingMessageId));
				setLoading(false);
				
				if (result.showAlert) {
					setValidationError(result.error);
					return;
				}
				throw new Error(result.error || 'Failed to process message');
			}
			
			setStreamInput(currentInput);

		} catch (error) {
			console.error('Error sending message:', error);
			setMessages(prev => prev.filter(msg => msg.id !== userMessageId && msg.id !== loadingMessageId));
			setLoading(false);
			setValidationError(error instanceof Error ? error.message : 'Failed to send message');
		}
	};

	const handleMessageReceived = (message: Message) => {
		setMessages(prev => {
			const withoutLoading = prev.filter(msg => msg.content !== 'loading');
			return [...withoutLoading, message];
		});
	};

	const handleSummaryReceived = useCallback((content: string) => {
		setSummaryContent(content);
	}, []);

	const handleNodeStatusChange = useCallback((statusOrUpdater: NodeStatus | ((prev: NodeStatus) => NodeStatus)) => {
		setNodeStatus(prev => {
			if (typeof statusOrUpdater === 'function') {
				return statusOrUpdater(prev);
			}
			return statusOrUpdater;
		});
	}, []);

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === 'Enter' && !e.shiftKey) {
			e.preventDefault();
			if (!loading && inputValue.trim()) {
				sendMessage();
			}
		}
	};

	const handleSelectConversation = async (selectedConversationId: string | null) => {
		setShowNewChatInput(false);
		
		if (selectedConversationId !== conversationId) {
			setMessages([]);
			setConversationId(selectedConversationId);
			setSummaryContent(null);
			setCurrentVideoUrl(null);
			setVideoMetadata(null);
			
				if (selectedConversationId) {
				setLoading(true);
				try {
					const response = await fetch(`/api/product/conversations/${selectedConversationId}/fetch`);
					const data = await response.json();
					
					if (!response.ok) {
						throw new Error(data.error || 'Failed to fetch conversation data');
					}
					
					const formattedMessages: Message[] = [];
					const { messages: messageData, summary, conversation } = data.data;

					if (conversation?.youtube_video_id) {
						setCurrentVideoUrl(`https://www.youtube.com/watch?v=${conversation.youtube_video_id}`);
					} else {
						setCurrentVideoUrl(null);
					}

					if (conversation?.video_metadata) {
						setVideoMetadata(conversation.video_metadata);
					} else {
						setVideoMetadata(null);
					}

					if (messageData && Array.isArray(messageData)) {
						messageData.forEach((msg: ConversationMessage) => {
							if (msg.content === "init") return;
							
							const messageType = msg.user_message === true ? 'user' : 'ai';
							
							formattedMessages.push({
								id: `${msg.id}-${messageType}`,
								type: messageType,
								content: msg.content
							});
						});
					}
					
					setMessages(formattedMessages);
					setSummaryContent(summary);

				} catch (error: unknown) {
					console.error('Error loading conversation:', error);
					setMessages([]);
					setSummaryContent(null);
				} finally {
					setLoading(false);
				}
			} else {
				setMessages([]);
				setSummaryContent(null);
				setCurrentVideoUrl(null);
				setVideoMetadata(null);
				setCurrentChatTitle("Processing…");
			}
		}
	};

	const handleConversationRenamed = useCallback(() => {
		setRefreshTrigger(prev => prev + 1);
	}, []);

	const handleNewChatCreated = useCallback((newConversationId: string, title: string, summary: string | null, videoUrl?: string, keepPending = false) => {
		setConversationId(newConversationId);
		setCurrentChatTitle(title);
		setSummaryContent(summary);
		setCurrentVideoUrl(videoUrl || null);
		// Don't clear video metadata here - it may have been set by the metadata fetch
		setMessages([]);
		setShowNewChatInput(false);
		setRefreshTrigger(prev => prev + 1);
		
		// Only clear pending state if keepPending is false
		if (!keepPending) {
			setCreatingConversation(false);
			setPendingConversations(prev => {
				const newSet = new Set(prev);
				newSet.delete(newConversationId);
				return newSet;
			});
		}
		
		// Update URL to reflect the new conversation
		if (newConversationId) {
			window.history.replaceState(null, '', `/product/${newConversationId}`);
		}
	}, []);

	const handleCreateNewClicked = useCallback(() => {
		setShowNewChatInput(true);
	}, []);

	const refreshConversationData = useCallback(async (conversationId: string) => {
		try {
			const response = await fetch(`/api/product/conversations/${conversationId}/fetch`);
			const data = await response.json();

			if (response.ok && data.data) {
				const { messages: messageData, summary, conversation } = data.data;

				// Update conversation title
				if (conversation?.name && conversation.name !== 'Processing...') {
					setCurrentChatTitle(conversation.name);
					setRefreshTrigger(prev => prev + 1);
				}

				// Update video metadata
				if (conversation?.video_metadata) {
					setVideoMetadata(conversation.video_metadata);
				}

				// Update summary
				if (summary) {
					setSummaryContent(summary);
				}

				// Update messages if any
				if (messageData && Array.isArray(messageData)) {
					const formattedMessages: Message[] = [];
					messageData.forEach((msg: ConversationMessage) => {
						if (msg.content === "init") return;

						const messageType = msg.user_message === true ? 'user' : 'ai';
						formattedMessages.push({
							id: `${msg.id}-${messageType}`,
							type: messageType,
							content: msg.content
				});
					});
					setMessages(formattedMessages);
		}

				// Trigger transcript refresh
				setTranscriptRefreshTrigger(prev => prev + 1);
			}
		} catch (error) {
			console.error('Error refreshing conversation data:', error);
		}
	}, [setCurrentChatTitle, setRefreshTrigger, setVideoMetadata, setSummaryContent, setMessages, setTranscriptRefreshTrigger]);

	const handleCreateNewBackground = useCallback(async (youtubeUrl: string) => {
		setCreatingConversation(true);
		setShowNewChatInput(false);

		const result = await createConversationBackground(youtubeUrl, {
			onSuccess: (result) => {
				// Add to pending conversations
				setPendingConversations(prev => new Set(prev).add(result.conversationId));

				// Subscribe to progress updates
				subscribeToProgress(result.conversationId, {
					onProgressUpdate: (progress) => {
						setConversationProgress(progress);
					},
					onTitleUpdate: (conversationId, oldTitle, newTitle) => {
						handleTitleUpdate(conversationId, oldTitle, newTitle);
					},
					onTranscriptFetched: (conversationId) => {
						setTranscriptRefreshTrigger(prev => prev + 1);
					},
					onTranscriptOptimized: (conversationId) => {
						setTranscriptRefreshTrigger(prev => prev + 1);
					},
					onCompleted: (conversationId) => {
						setPendingConversations(prev => {
							const newSet = new Set(prev);
							newSet.delete(conversationId);
							return newSet;
						});
						setConversationProgress(null);
						refreshConversationData(conversationId);
					},
					onFailed: (conversationId, error) => {
						setPendingConversations(prev => {
							const newSet = new Set(prev);
							newSet.delete(conversationId);
							return newSet;
						});
						setValidationError(error);
						setConversationProgress(null);
					},
					onError: (error) => {
						setValidationError(error);
					}
				});

				// Call handleNewChatCreated to update UI state
				handleNewChatCreated(
					result.conversationId,
					result.title,
					null,
					result.videoUrl,
					true // Keep pending state
				);
			},
			onError: (errorMessage) => {
				setValidationError(errorMessage);
			}
		});

		setCreatingConversation(false);
	}, [createConversationBackground, subscribeToProgress, setPendingConversations, setConversationProgress, setCurrentChatTitle, handleTitleUpdate, setTranscriptRefreshTrigger, setValidationError, refreshConversationData, handleNewChatCreated]);









	const handleCancelNewChat = useCallback(() => {
		setShowNewChatInput(false);
	}, []);


	useEffect(() => {
		async function fetchConversationDetails() {
			if (!conversationId) {
				setCurrentChatTitle("Processing…");
				return;
			}
			try {
				const response = await fetch(`/api/product/conversations`);
				const data = await response.json();
				if (data.data && Array.isArray(data.data)) {
					const conversation = data.data.find((conv: ConversationListItem) => conv.id === conversationId);
					if (conversation) {
						setCurrentChatTitle(conversation.name);
					}
				}
			} catch (error: unknown) {
				console.error("Error fetching conversation title:", error);
			}
		}
		fetchConversationDetails();
	}, [conversationId, refreshTrigger]);

	useEffect(() => {
		if (chatContainerRef.current) {
			setTimeout(() => {
				chatContainerRef.current?.scrollTo({
					top: chatContainerRef.current.scrollHeight,
					behavior: 'smooth'
				});
			}, 100);
		}
	}, [messages, loading, nodeStatus]);

	useEffect(() => {
		const handleKeyDown = (event: KeyboardEvent) => {
			if (event.key === 'Escape') {
				handleCancelNewChat();
			}
		};

		if (showNewChatInput) {
			window.addEventListener('keydown', handleKeyDown);
		}

		return () => {
			window.removeEventListener('keydown', handleKeyDown);
		};
	}, [showNewChatInput, handleCancelNewChat]);

	// Update browser tab title when conversation title changes
	useEffect(() => {
		const baseTitle = 'Videotext';
		if (currentChatTitle && currentChatTitle !== 'Processing…') {
			document.title = `${currentChatTitle} - ${baseTitle}`;
		} else {
			document.title = baseTitle;
		}
	}, [currentChatTitle]);



	useEffect(() => {
		// Handle changes in initialConversationId (including when it becomes null)
		const conversationIdToSelect = initialConversationId ?? null;
		if (conversationIdToSelect !== conversationId) {
			handleSelectConversation(conversationIdToSelect);
		}
	}, [initialConversationId]);

	// Check for existing processing when conversation is selected
	useEffect(() => {
		if (conversationId && checkExistingProcessingHook) {
			checkExistingProcessingHook(conversationId, {
				onProgressUpdate: (progress) => {
					setConversationProgress(progress);
				},
				onTitleUpdate: (conversationId, oldTitle, newTitle) => {
					handleTitleUpdate(conversationId, oldTitle, newTitle);
				},
				onTranscriptFetched: (conversationId) => {
					setTranscriptRefreshTrigger(prev => prev + 1);
				},
				onTranscriptOptimized: (conversationId) => {
					setTranscriptRefreshTrigger(prev => prev + 1);
				},
				onCompleted: (conversationId) => {
					setPendingConversations(prev => {
						const newSet = new Set(prev);
						newSet.delete(conversationId);
						return newSet;
					});
					setConversationProgress(null);
					refreshConversationData(conversationId);
				},
				onFailed: (conversationId, error) => {
					setPendingConversations(prev => {
						const newSet = new Set(prev);
						newSet.delete(conversationId);
						return newSet;
					});
					setValidationError(error);
					setConversationProgress(null);
				},
				onError: (error) => {
					setValidationError(error);
				}
			}).then((cleanup) => {
				if (cleanup) {
					// Add to pending conversations if processing was found
					setPendingConversations(prev => new Set(prev).add(conversationId));
				}
			});
		}
	}, [conversationId, checkExistingProcessingHook, setConversationProgress, setCurrentChatTitle, handleTitleUpdate, setTranscriptRefreshTrigger, setPendingConversations, setValidationError, refreshConversationData]);

	const handleToggleChatMinimized = () => {
		setChatMinimized(!chatMinimized);
	};

	const handleToggleSidebar = () => {
		const shouldAutoCollapse = !chatMinimized || rightPanelExpanded;
		const isSidebarEffectivelyCollapsed = sidebarManualOverride === 'collapsed' || (sidebarManualOverride === null && shouldAutoCollapse);

		if (isSidebarEffectivelyCollapsed) {
			setSidebarManualOverride('expanded');
		} else {
			setSidebarManualOverride('collapsed');
		}
	};

	const handleVideoHover = (isHovering: boolean) => {
		// Clear any existing timeout
		if (hoverTimeoutRef.current) {
			clearTimeout(hoverTimeoutRef.current);
		}

		// Set a new timeout
		hoverTimeoutRef.current = setTimeout(() => {
			// Removed setIsVideoHovered as it's not used
			if (isHovering && !isPinned) {
				setRightPanelExpanded(true);
			} else if (!isHovering && !isPinned) {
				setRightPanelExpanded(false);
			}
		}, 150);
	};

	const handlePinClick = () => {
		const newPinnedState = !isPinned;
		setIsPinned(newPinnedState);
		setRightPanelExpanded(newPinnedState);
	};

	const handlePlayerReady = (seekFunction: (seconds: number) => void) => {
		setVideoSeekFunction(() => seekFunction);
	};

	// Cleanup timeout on unmount
	useEffect(() => {
		return () => {
			if (hoverTimeoutRef.current) {
				clearTimeout(hoverTimeoutRef.current);
			}
			// Clear all title animation timeouts
			titleTimeoutRefs.current.forEach((timeout) => {
				clearTimeout(timeout);
			});
			titleTimeoutRefs.current.clear();
		};
	}, []);

	// Calculate effective sidebar state
	const shouldAutoCollapse = !chatMinimized || rightPanelExpanded;
	const isSidebarEffectivelyCollapsed = sidebarManualOverride === 'collapsed' || (sidebarManualOverride === null && shouldAutoCollapse);

	// Calculate conversation ready state
	const conversationReady = !!(
		conversationId && 
		!creatingConversation && 
		!pendingConversations.has(conversationId) &&
		(!conversationProgress || conversationProgress.currentStep === 'completed')
	);

	return (
		<>
			<DifyStreamProcessor
				inputText={streamInput}
				conversationId={conversationId}
				onStreamStart={() => setLoading(true)}
				onStreamEnd={() => {
					setLoading(false);
					setStreamInput(''); 
				}}
				onMessageReceived={handleMessageReceived}
				onConversationIdChange={setConversationId}
				onNodeStatusChange={handleNodeStatusChange}
				onSummaryReceived={handleSummaryReceived}
				onConversationRenamed={handleConversationRenamed}
			/>
			<Group className={InterfaceClasses.interfaceRoot}>
				<Box 
					className={InterfaceClasses.sidebarContainer}
					data-chat-expanded={!chatMinimized}
					data-collapsed={isSidebarEffectivelyCollapsed}
				>
					<SavedChats
						onSelectConversation={handleSelectConversation}
						currentConversationId={conversationId}
						onConversationRenamed={handleConversationRenamed}
						isCollapsed={isSidebarEffectivelyCollapsed}
						refreshTrigger={refreshTrigger}
						onCreateNewClicked={handleCreateNewClicked}
						onToggleCollapse={handleToggleSidebar}
						creatingConversation={creatingConversation}
						pendingConversations={pendingConversations}
						titleAnimations={titleAnimations}
						onTitleAnimationComplete={(conversationId: string) => {
							// Clear the timeout if it exists
							const existingTimeout = titleTimeoutRefs.current.get(conversationId);
							if (existingTimeout) {
								clearTimeout(existingTimeout);
								titleTimeoutRefs.current.delete(conversationId);
							}
							
							setTitleAnimations(prev => {
								const newMap = new Map(prev);
								newMap.delete(conversationId);
								return newMap;
							});
						}}
					/>
				</Box>
				<Box 
					className={InterfaceClasses.contentContainer}
					data-chat-expanded={!chatMinimized}
					data-sidebar-collapsed={isSidebarEffectivelyCollapsed}
					data-panel-expanded={rightPanelExpanded}
				>
					<SummaryTranscriptView
						conversationId={conversationId}
						summaryContent={summaryContent}
						pendingConversations={pendingConversations}
						currentVideoUrl={currentVideoUrl}
						videoTitle={videoMetadata?.title}
						onSummaryReceived={handleSummaryReceived}
						showNewChatInput={showNewChatInput || (!conversationId && messages.length === 0 && !loading)}
						onConversationCreated={handleNewChatCreated}
						onNewChatError={(error: string) => setValidationError(error)}
						onNewChatCancel={conversationId ? handleCancelNewChat : undefined}
						onCreateBackground={handleCreateNewBackground}
						onTimestampClick={videoSeekFunction}
						onPinVideo={() => {
							setIsPinned(true);
							setRightPanelExpanded(true);
						}}
						onExpandVideo={() => setRightPanelExpanded(true)}
						isCollapsed={isSidebarEffectivelyCollapsed}
						transcriptRefreshTrigger={transcriptRefreshTrigger}
						conversationProgress={conversationProgress}
						creatingConversation={creatingConversation}
					/>
				</Box>
				<Box 
					className={InterfaceClasses.rightContainer} 
					data-chat-expanded={!chatMinimized}
					data-panel-expanded={rightPanelExpanded}
				>
					{/* Top bar for right container */}
					{(!chatMinimized || rightPanelExpanded) && (
						<Group className={InterfaceClasses.rightTopBar} data-collapsed={isSidebarEffectivelyCollapsed}>
							<Text fw={600} className={InterfaceClasses.rightTopBarTitle}>
								Chat
							</Text>
							<ActionIcon
								variant="subtle"
								color="gray"
								size="sm"
								onClick={() => {
									setRightPanelExpanded(false);
									setIsPinned(false);
									setChatMinimized(true);
								}}
								className={InterfaceClasses.rightTopBarClose}
							>
								<IconX size={16} />
							</ActionIcon>
						</Group>
					)}

					{conversationId && currentVideoUrl && videoMetadata && (	
							<VideoMetadataCard 
								metadata={videoMetadata}
								videoUrl={currentVideoUrl}
								chatMinimized={chatMinimized}
								onVideoHover={handleVideoHover}
								isPinned={isPinned}
								onPinClick={handlePinClick}
								onPlayerReady={handlePlayerReady}
							/>
					)}

						{conversationId && (
							<ChatWindow
								currentChatTitle={currentChatTitle}
								messages={messages}
								loading={loading}
								nodeStatus={nodeStatus}
								inputValue={inputValue}
								onInputChange={setInputValue}
								onSendMessage={sendMessage}
								onKeyDown={handleKeyDown}
								validationError={validationError}
								onClearValidationError={() => setValidationError(null)}
								chatContainerRef={chatContainerRef}
								chatMinimized={chatMinimized}
								onToggleChatMinimized={handleToggleChatMinimized}
								isCollapsed={isSidebarEffectivelyCollapsed}
								conversationReady={conversationReady}
							/>
						)}	

				</Box>
			</Group>
		</>
	);
}