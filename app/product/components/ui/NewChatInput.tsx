'use client';

import React, { useState } from 'react';
import { Group, Button, Input, Alert, Stack, Box, Flex } from '@mantine/core';
import { IconAlertCircle } from '@tabler/icons-react';
import { IoCreateSharp } from "react-icons/io5";
import classes from '../../theme/NewChatInput.module.css';
import { useConversationCreation } from '@/app/hooks/useConversationCreation';

interface NewChatInputProps {
  onConversationCreated: (conversationId: string, title: string, summary: string | null, videoUrl?: string) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  variant?: 'sidebar' | 'main'; // Different variants for different use cases
  onCancel?: () => void; // Add cancel callback for main variant
  onCreateNew?: () => void; // Add callback for sidebar variant to show main interface
  onCreateBackground?: (youtubeUrl: string) => void; // Add callback for background creation
  useBackground?: boolean; // Whether to use background processing
}

export default function NewChatInput({
  onConversationCreated,
  onError,
  disabled = false,
  variant = 'main',
  onCancel,
  onCreateNew,
  onCreateBackground,
  useBackground = false
}: NewChatInputProps) {
  const [inputValue, setInputValue] = useState('');
  const { createConversation, createConversationBackground, isCreating, error, clearError } = useConversationCreation();

  const handleSubmit = async () => {
    if (!inputValue.trim()) return;

    // Clear any previous errors
    clearError();

    // Choose the appropriate creation method
    const createMethod = useBackground ? createConversationBackground : createConversation;

    const result = await createMethod(inputValue, {
      onSuccess: (result) => {
        // Clear input on success
        setInputValue('');

        // Notify parent component
        onConversationCreated(
          result.conversationId,
          result.title,
          null, // No summary available yet
          result.videoUrl
        );
      },
      onError: (errorMessage) => {
        onError?.(errorMessage);
      }
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (!isCreating && inputValue.trim()) {
        handleSubmit();
      }
    }
  };

  // Sidebar variant - just a simple "Create new" button
  if (variant === 'sidebar') {
    return (
      <Button 
        variant="sidebar" 
        leftSection={<IoCreateSharp className="-mt-[0.1em]" color="var(--mantine-color-gray-7)" size={18}/>}
        onClick={onCreateNew}
        disabled={disabled}
        style={{
          transition: 'all 200ms ease'
        }}
      >
        Create new
      </Button>
    );
  }

  // Main variant - full input interface
  return (
    <Box className={classes.newChatInputRoot}>
    <Stack 
      align="center" 
      className={classes.newChatInput}
      gap="xs" 
      style={{ width: '500px', maxWidth: '100%', paddingLeft: '1rem', paddingRight: '1rem' }}
    >
      

      
      <Group gap="xs" w="100%">
        <Input
          flex={1}
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Please insert YouTube link"
          aria-label="YouTube link input"
          disabled={isCreating || disabled}
        />
      </Group>
      {error && (
        <Alert
          icon={<IconAlertCircle size="1rem" />}
          custom-color="red"
          variant="outline"
          onClose={clearError}
          withCloseButton
          w="100%"
        >
          {error}
        </Alert>
      )}
      <Flex w="100%" mt="xs" gap="sm">
        {onCancel && (
          <Button
            variant="subtle"
            color="gray"
            onClick={onCancel}
            style={{ width: '25%' }}
          >
            Cancel
          </Button>
        )}
        <Button
          onClick={handleSubmit}
          disabled={isCreating || disabled || !inputValue.trim()}
          variant="outline"
          style={{ width: onCancel ? '75%' : '100%' }}
        >
          {isCreating ? (
            <svg className="animate-spin h-5 w-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 818-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : 'Create videotext'}
        </Button>
      </Flex>
    </Stack>
    </Box>
  );
} 