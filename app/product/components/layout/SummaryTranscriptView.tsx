'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { Box, Group, Text, ScrollArea, Switch, LoadingOverlay, Loader } from '@mantine/core';
import ReactMarkdown from 'react-markdown';
import { TranscriptSegment } from '../../lib/types';
import ThreeStepProgress, { ConversationProgress } from '../ui/ThreeStepProgress';
import NewChatInput from '../ui/NewChatInput';
import classes from '../../theme/Summary.module.css';

interface SummaryTranscriptViewProps {
  conversationId: string | null;
  summaryContent: string | null;
  pendingConversations?: Set<string>;
  currentVideoUrl?: string | null;
  videoTitle?: string | null;
  onSummaryReceived?: (summaryContent: string) => void;
  showNewChatInput?: boolean;
  onConversationCreated?: (conversationId: string, title: string, summary: string | null, videoUrl?: string) => void;
  onNewChatError?: (error: string) => void;
  onNewChatCancel?: () => void;
  onCreateBackground?: (youtubeUrl: string) => void;
  onTimestampClick?: ((seconds: number) => void) | null;
  onPinVideo?: () => void;
  onExpandVideo?: () => void;
  isCollapsed?: boolean;
  transcriptRefreshTrigger?: number; // Add this new prop to force transcript refresh
  conversationProgress?: ConversationProgress | null;
  creatingConversation?: boolean;
}

const SummaryTranscriptView: React.FC<SummaryTranscriptViewProps> = ({ 
  conversationId, 
  summaryContent, 
  pendingConversations = new Set(),
  currentVideoUrl,
  videoTitle,
  onSummaryReceived,
  showNewChatInput = false,
  onConversationCreated,
  onNewChatError,
  onNewChatCancel,
  onCreateBackground,
  onTimestampClick,
  onPinVideo,
  onExpandVideo,
  isCollapsed = false,
  transcriptRefreshTrigger = 0,
  conversationProgress = null,
  creatingConversation = false
}) => {
  const [showProgress, setShowProgress] = useState(false);
  const [transcriptLength, setTranscriptLength] = useState(0);
  const [showSummary, setShowSummary] = useState<boolean>(false);
  const [transcriptData, setTranscriptData] = useState<TranscriptSegment[]>([]);
  const [rawTranscriptData, setRawTranscriptData] = useState<TranscriptSegment[]>([]);
  const [optimizedTranscriptData, setOptimizedTranscriptData] = useState<TranscriptSegment[]>([]);
  const [showOptimizedTranscript, setShowOptimizedTranscript] = useState<boolean>(true);
  const [transcriptError, setTranscriptError] = useState<string | null>(null);
  const [userHasManuallySelected, setUserHasManuallySelected] = useState(false);
  const [hasOptimizedTranscript, setHasOptimizedTranscript] = useState<boolean>(false);

  useEffect(() => {
    // Default to AI Summary for existing conversations, transcript for new ones
    setShowSummary(!!conversationId); // true for existing conversations, false for new
    setUserHasManuallySelected(false);
    setRawTranscriptData([]);
    setOptimizedTranscriptData([]);
    setHasOptimizedTranscript(false);
    setShowOptimizedTranscript(true); // Default to optimized when available
    setTranscriptError(null);
  }, [conversationId]);

  const isConversationPending = conversationId && pendingConversations.has(conversationId);

  // Utility function to format milliseconds to HH:MM:SS
  const formatTimestamp = (milliseconds: number): string => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
  };

  // Function to parse transcript data from various formats
  const parseTranscriptData = (transcriptData: unknown): TranscriptSegment[] => {
    if (!transcriptData) return [];
    
    // Type guard function to check if an object has a property
    const hasProperty = <K extends string>(obj: unknown, prop: K): obj is Record<K, unknown> => {
      return typeof obj === 'object' && obj !== null && prop in obj;
    };
    
    // If transcriptData is a string (especially for optimized transcripts), try to parse it as JSON
    if (typeof transcriptData === 'string') {
      try {
        const parsedData = JSON.parse(transcriptData);
        // Recursively call parseTranscriptData with the parsed object
        return parseTranscriptData(parsedData);
      } catch (parseError) {
        console.warn('Failed to parse transcript string as JSON:', parseError);
        // If it's not valid JSON, treat it as plain text
        return [{
          text: transcriptData,
          duration: 0,
          offset: 0,
          lang: 'en'
        }];
      }
    }
    
    // Handle new combined transcript structure
    if (hasProperty(transcriptData, 'transcript') && 
        hasProperty(transcriptData.transcript, 'content') &&
        Array.isArray(transcriptData.transcript.content)) {
      return transcriptData.transcript.content as TranscriptSegment[];
    }
    
    // Handle different transcript formats for backward compatibility
    // 1. Check for nested transcript.content structure (optimized transcripts)
    if (hasProperty(transcriptData, 'transcript') && 
        hasProperty(transcriptData.transcript, 'content') && 
        Array.isArray(transcriptData.transcript.content)) {
      return transcriptData.transcript.content as TranscriptSegment[];
    }
    // 2. Check for direct content array (raw transcripts)
    else if (hasProperty(transcriptData, 'content') && Array.isArray(transcriptData.content)) {
      return transcriptData.content as TranscriptSegment[];
    } 
    // 3. Check for direct array
    else if (Array.isArray(transcriptData)) {
      return transcriptData as TranscriptSegment[];
    } 
    // 5. Handle single segment object
    else if (hasProperty(transcriptData, 'text') && typeof transcriptData.text === 'string') {
      return [{
        text: transcriptData.text,
        duration: (hasProperty(transcriptData, 'duration') && typeof transcriptData.duration === 'number') ? transcriptData.duration : 0,
        offset: (hasProperty(transcriptData, 'offset') && typeof transcriptData.offset === 'number') ? transcriptData.offset : 0,
        lang: (hasProperty(transcriptData, 'lang') && typeof transcriptData.lang === 'string') ? transcriptData.lang : 'en'
      }];
    } else {
      console.warn('Unexpected transcript format:', transcriptData);
      return [];
    }
  };

  // Function to fetch both raw and optimized transcripts
  const fetchTranscript = useCallback(async () => {
    if (!conversationId) return;

    setTranscriptError(null);

    try {
      // Fetch both raw and optimized transcripts from database
      const apiResponse = await fetch('/api/product/fetch_transcript_both', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conversationId,
        }),
      });

      if (!apiResponse.ok) {
        const errorData = await apiResponse.json();
        throw new Error(errorData.error || 'Failed to fetch transcript');
      }

      const responseData = await apiResponse.json();
      
      if (responseData.success) {
        // Parse both transcript types
        const rawSegments = parseTranscriptData(responseData.rawTranscript);
        const optimizedSegments = parseTranscriptData(responseData.optimizedTranscript);
        

        
        setRawTranscriptData(rawSegments);
        setOptimizedTranscriptData(optimizedSegments);
        setHasOptimizedTranscript(responseData.hasOptimized);
        
        // Calculate transcript length for progress estimation
        const transcriptText = rawSegments.map(segment => segment.text).join(' ');
        setTranscriptLength(transcriptText.length);
        
        // Set the main transcript data based on what's available and current toggle state
        if (showOptimizedTranscript && optimizedSegments.length > 0) {
          setTranscriptData(optimizedSegments);
        } else if (rawSegments.length > 0) {
          setTranscriptData(rawSegments);
          if (!responseData.hasOptimized) {
            setShowOptimizedTranscript(false); // Default to raw if no optimized version
          }
        } else {
          setTranscriptData([]);
        }
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error fetching transcript:', error);
      setTranscriptError(error instanceof Error ? error.message : 'Failed to load transcript');
    }
  }, [conversationId, showOptimizedTranscript]);

  // Fetch transcript when conversation changes
  useEffect(() => {
    if (conversationId) {
      fetchTranscript();
    }
  }, [conversationId, fetchTranscript]);

  // Fetch transcript when switching to transcript view if not already loaded
  useEffect(() => {
    if (conversationId && !showSummary && rawTranscriptData.length === 0 && optimizedTranscriptData.length === 0) {
      fetchTranscript();
    }
  }, [conversationId, showSummary, rawTranscriptData.length, optimizedTranscriptData.length, fetchTranscript]);

  // Update transcript data when toggle changes
  useEffect(() => {
    if (showOptimizedTranscript && optimizedTranscriptData.length > 0) {
      setTranscriptData(optimizedTranscriptData);
    } else if (rawTranscriptData.length > 0) {
      setTranscriptData(rawTranscriptData);
    }
  }, [showOptimizedTranscript, rawTranscriptData, optimizedTranscriptData]);

  // Force transcript refresh when trigger changes (for progressive updates during creation)
  useEffect(() => {
    if (transcriptRefreshTrigger > 0 && conversationId) {
      fetchTranscript();
    }
  }, [transcriptRefreshTrigger, conversationId, fetchTranscript]);

  // When summary content becomes available for existing conversations, switch to summary view
  useEffect(() => {
    if (!userHasManuallySelected && summaryContent && conversationId && !pendingConversations.has(conversationId)) {
      // This is an existing conversation that just loaded its summary - show it
      setShowSummary(true);
    }
  }, [summaryContent, conversationId, pendingConversations, userHasManuallySelected]);

  // Set initial toggle state based on available content (only if user hasn't manually selected)
  useEffect(() => {
    if (!userHasManuallySelected) {
      if (summaryContent) {
        // If summary is available, always show AI Summary
        setShowSummary(true);
      } else {
        // Check if this is a new conversation being created
        const isNewConversation = pendingConversations.has(conversationId || '');
        
        if (isNewConversation && transcriptData.length > 0) {
          // Progressive display for new conversations: show transcript first, then upgrade
          setShowSummary(false);
          
          // Auto-switch to optimized transcript if available
          if (optimizedTranscriptData.length > 0 && !showOptimizedTranscript) {
            setShowOptimizedTranscript(true);
          }
        }
        // For existing conversations without content loaded yet, keep current state (summary mode)
      }
    }
  }, [summaryContent, transcriptData, optimizedTranscriptData, userHasManuallySelected, showOptimizedTranscript, pendingConversations, conversationId]);

  // Progress handling
  useEffect(() => {
    if (isConversationPending) {
      setShowProgress(true);
    } else if (summaryContent) {
      setShowProgress(false);
    }
  }, [isConversationPending, summaryContent]);

  const handleToggleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setShowSummary(event.currentTarget.checked);
    setUserHasManuallySelected(true);
  };

  const handleTranscriptTypeToggleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setShowOptimizedTranscript(event.currentTarget.checked);
  };

  const handleTimestampClick = (milliseconds: number) => {
    if (onTimestampClick) {
      const seconds = Math.floor(milliseconds / 1000);
      onTimestampClick(seconds);
      // Expand and pin the video when clicking a timestamp
      onExpandVideo?.();
      onPinVideo?.();
    }
  };

  const renderSummaryContent = () => {
    if (summaryContent) {
      return (
        <div className={classes.summaryContent}>
          <ReactMarkdown>{summaryContent}</ReactMarkdown>
        </div>
      );
    }

    // If conversation is pending, don't show loading in content area - progress bar handles it
    if (isConversationPending && showProgress) {
      return (
        <div className={classes.summaryContent}>
          {/* Progress is handled by ThreeStepProgress component */}
        </div>
      );
    }

    return (
      <div className={classes.summaryContent}>
        <LoadingOverlay visible={true} />
      </div>
    );
  };

  const renderTranscriptContent = () => {

    if (transcriptError) {
      return (
        <div className={classes.summaryContent}>
          <Text c="red">Error loading transcript: {transcriptError}</Text>
        </div>
      );
    }

    if (transcriptData.length === 0) {
      return (
        <div className={classes.summaryContent}>
          <Text c="dimmed">No transcript available</Text>
        </div>
      );
    }

    return (
      <div className={classes.summaryContent}>
        {videoTitle && (
          <Text size="sm" fw={500} className={classes.transcriptTitle}>
            {videoTitle}
          </Text>
        )}
        <ScrollArea h="100%" type="auto">
          <div className={classes.transcriptContainer}>
            {Array.isArray(transcriptData) ? transcriptData.map((segment, index) => (
              <div 
                key={index}
                className={classes.transcriptSegment}
              >
                <div 
                  className={classes.transcriptTimestamp}
                  onClick={() => handleTimestampClick(segment.offset)}
                  style={{ cursor: onTimestampClick ? 'pointer' : 'default' }}
                  title={onTimestampClick ? 'Click to jump to this time in the video' : undefined}
                >
                  {formatTimestamp(segment.offset)}
                </div>
                <div className={classes.transcriptText}>
                  {segment.text}
                </div>
              </div>
            )) : (
              <div className={classes.transcriptSegment}>
                <div className={classes.transcriptText}>
                  No transcript data available
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </div>
    );
  };

  return (
    <>
      <Box className={classes.summary}>
        {conversationId && (
          <>
            <Group className={classes.toggleContainer} data-collapsed={isCollapsed} style={{ alignItems: 'flex-start' }}>
              <Group gap="md" style={{ alignItems: 'center', flexShrink: 0 }}>
                <Switch
                  checked={showSummary}
                  onChange={handleToggleChange}
                  size="sm"
                  color="green"
                  withThumbIndicator={false}
                  label={showSummary ? "AI Summary" : "Transcript"}
                  classNames={{
                    root: classes.toggleSwitchRoot,
                    input: classes.toggleSwitchInput,
                    track: classes.toggleSwitchTrack,
                    thumb: classes.toggleSwitchThumb,
                    trackLabel: classes.toggleSwitchLabel,
                  }}
                  styles={showSummary ? {label: {color: 'var(--mantine-color-green-6)'}} : {label: {color: 'var(--mantine-color-black)'}, track: {backgroundColor: 'var(--mantine-color-black)'}}}
                />
                {!showSummary && hasOptimizedTranscript && rawTranscriptData.length > 0 && optimizedTranscriptData.length > 0 && (
                  <>
                    <Switch
                      checked={showOptimizedTranscript}
                      onChange={handleTranscriptTypeToggleChange}
                      size="sm"
                      color="gray"
                      label={showOptimizedTranscript ? "Optimized" : "Raw"}
                      classNames={{
                        root: classes.toggleSwitchRoot,
                        input: classes.toggleSwitchInput,
                        track: classes.toggleSwitchTrack,
                        thumb: classes.toggleSwitchThumb,
                        trackLabel: classes.toggleSwitchLabel,
                      }}
                      styles={showOptimizedTranscript ? {label: {color: 'var(--mantine-color-gray-6)'}} : {label: {color: 'var(--mantine-color-gray-6)'}}}
                    />
                  </>
                )}

              </Group>
              <Box style={{ flex: 1, display: 'flex', justifyContent: 'flex-end' }}>
              {isConversationPending && conversationProgress && (
                <ThreeStepProgress 
                  conversationProgress={conversationProgress || undefined}
                  transcriptLength={transcriptLength}
                />
              )}
              </Box>
            </Group>
            
            {showSummary ? renderSummaryContent() : renderTranscriptContent()}
          </>
        )}
        
        {showNewChatInput && (
          <div className={classes.overlay} data-collapsed={isCollapsed}>
            <NewChatInput
              onConversationCreated={onConversationCreated!}
              onError={onNewChatError}
              onCancel={onNewChatCancel}
              onCreateBackground={onCreateBackground}
            />
          </div>
        )}
      </Box>
    </>
  );
};

export default SummaryTranscriptView; 