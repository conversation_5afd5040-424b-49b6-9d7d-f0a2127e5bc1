.interfaceRoot {
    width: 100%;
    height: 100%;
    display: flex;
}

.sidebarContainer {
    position: fixed;
    top: 0;
    left: 0;
    width: 250px;
    height: 100%;
    transition: width 0.25s ease;
    z-index: 3;
    flex-shrink: 0;
}

.sidebarContainer[data-collapsed="true"] {
    width: 60px;
}

.contentContainer {
    position: relative;
    flex: 1;
    z-index: 2;
    margin-left: 250px;
    margin-right: 250px;
    transition: margin 0.25s ease;
    background-color: var(--mantine-color-white);
    min-height: 100vh;
}

.contentContainer[data-sidebar-collapsed="true"] {
    margin-left: 60px;
}

.contentContainer[data-chat-expanded="true"] {
    margin-right: 500px;
}

/* When both chat is expanded and sidebar is not collapsed, overlay the sidebar */
.contentContainer[data-chat-expanded="true"][data-sidebar-collapsed="false"] {
    margin-left: 60px;
}

.rightContainer {
    position: fixed;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    top: 0;
    right: 0;
    width: 250px;
    height: 100%;
    flex-shrink: 0;
    transition: width 0.25s ease;
    overflow: hidden; /* Prevent content overflow */
    padding: var(--mantine-spacing-md);
    background-color: var(--mantine-color-white);
    z-index: 1;
}

.rightContainer[data-chat-expanded="true"] {
    border-left: 1px solid var(--mantine-color-gray-3);
    padding: 0;
}

.rightContainer > * {
    flex-shrink: 0; /* Prevent children from shrinking */
    min-height: 0; /* Allow children to be smaller than content */
}

.rightContainer[data-chat-expanded="true"] {
    z-index: 4;
    width: 500px;
}

.rightContainer[data-panel-expanded="true"] {
    width: 500px;
}

.contentContainer[data-panel-expanded="true"] {
    margin-right: 500px;
}

/* Override for when both panel is expanded and sidebar is not collapsed */
.contentContainer[data-panel-expanded="true"][data-sidebar-collapsed="false"] {
    margin-left: 60px;
}

.rightTopBar {
    position: fixed;
    top: 0;
    right: 0;
    height: 50px;
    width: 250px;
    padding: 0 20px;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(16px);    border-left: 1px solid var(--mantine-color-gray-3);
    transition: width 0.25s ease;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
    display: none;
}

.rightTopBar:after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--mantine-color-gray-3);
    height: 1px;
}

.rightContainer[data-chat-expanded="true"] .rightTopBar {
    display: flex;
    width: 500px;
    border-left: 1px solid var(--mantine-color-gray-3);
}

.rightTopBarTitle {
    font-size: var(--mantine-font-size-xs);
    text-transform: uppercase;
    font-weight: 600;
    color: var(--mantine-color-gray-7);
    margin: 0;
}

.rightTopBarClose {
    transition: all 0.2s ease;
}

.rightTopBarClose:hover {
    background-color: var(--mantine-color-gray-2);
}

/* Add top padding to right container content to account for top bar */
.rightContainer[data-chat-expanded="true"] {
    padding-top: 50px;
}

