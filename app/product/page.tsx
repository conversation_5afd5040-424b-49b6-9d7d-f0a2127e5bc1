'use client';

import ChatInterface from './ChatInterface';
import { useUserData } from '@/contexts/UserDataContext';
import { useRouter, usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import SuccessAlert from '../components/ui/SuccessAlert';
import { Loader } from '@mantine/core';

export default function ProductPage() {
	const { user, isLoading } = useUserData();
	const router = useRouter();
	const pathname = usePathname();
	const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);

	// Extract conversation ID from URL path
	useEffect(() => {
		const pathParts = pathname.split('/');
		if (pathParts.length >= 3 && pathParts[1] === 'product' && pathParts[2]) {
			setCurrentConversationId(pathParts[2]);
		} else {
			setCurrentConversationId(null);
		}
	}, [pathname]);

	useEffect(() => {
		if (!isLoading && !user) {
			router.push('/auth');
		}
	}, [user, isLoading, router]);

	if (isLoading || !user) {
		// You can add a loading spinner here
		return <div className="fixed top-0 left-0 w-full h-full flex justify-center items-center">
			<Loader />
		</div>;
	}

	return (
		<>
			<SuccessAlert />
			<ChatInterface initialConversationId={currentConversationId} />
		</>
	);
}