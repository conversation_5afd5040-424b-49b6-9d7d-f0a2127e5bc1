import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { supabaseAdmin } from '@/lib/supabase/admin';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const conversationId = searchParams.get('conversation_id');

  if (!conversationId) {
    return NextResponse.json({ message: 'Conversation ID is required' }, { status: 400 });
  }

  try {
    // Get user session
    const supabase = await createClient();
    const {
      data: { user }
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' }, 
        { status: 401 }
      );
    }

    console.log(`Fetching messages for conversation: ${conversationId}, user: ${user.id}`);
    
    // Fetch messages from Supabase messages table
    const { data: messages, error } = await supabase
      .from('messages')
      .select('id, content, message_number, created_at, user_message')
      .eq('conversation_id', conversationId)
      .eq('user_id', user.id)
      .order('message_number', { ascending: true });

    if (error) {
      console.error('Error fetching messages:', error);
      return NextResponse.json(
        { message: 'Error fetching messages', error: error.message }, 
        { status: 500 }
      );
    }

    // Transform the data to match the expected format from the frontend
    const transformedData = {
      data: messages?.map(msg => ({
        id: msg.id,
        query: msg.user_message ? msg.content : null, // Only set query for user messages
        answer: !msg.user_message ? msg.content : null, // Only set answer for AI messages
        created_at: Math.floor(new Date(msg.created_at).getTime() / 1000),
        message_number: msg.message_number,
        user_message: msg.user_message
      })) || []
    };

    console.log(`Fetched ${transformedData.data.length} messages`);
    return NextResponse.json(transformedData);
    
  } catch (error: unknown) {
    console.error('Error fetching messages:', error);
    
    return NextResponse.json(
      { message: 'Error fetching messages', error: error instanceof Error ? error.message : 'Unknown error' }, 
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { conversationId, content } = body;

    if (!conversationId || !content) {
      return NextResponse.json({ error: 'Conversation ID and content are required' }, { status: 400 });
    }

    const supabase = await createClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // First, resolve the actual Supabase conversation ID
    const { data: conversation, error: conversationError } = await supabase
      .from('conversations')
      .select('id, dify_summary_id')
      .or(`id.eq.${conversationId},dify_summary_id.eq.${conversationId}`)
      .eq('user_id', user.id)
      .single();

    if (conversationError) {
      console.error('Error finding conversation:', conversationError);
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
    }

    const supabaseConversationId = conversation.id;

    // Get the current message count to determine message_number
    const { count, error: countError } = await supabase
      .from('messages')
      .select('id', { count: 'exact', head: true })
      .eq('conversation_id', supabaseConversationId);
    
    if (countError) {
      console.error('Error getting message count:', countError);
      return NextResponse.json({ error: 'Failed to get message count' }, { status: 500 });
    }
    
    const nextMessageNumber = (count ?? 0) + 1;

    // Store the AI message in the messages table using admin client
    const { data: newMessage, error: messageError } = await supabaseAdmin
      .from('messages')
      .insert({
        conversation_id: supabaseConversationId,
        user_id: user.id,
        message_number: nextMessageNumber,
        content: content,
        user_message: false
      })
      .select()
      .single();

    if (messageError) {
      console.error('Error storing AI message:', messageError);
      return NextResponse.json({ 
        error: 'Failed to store AI message',
        details: messageError.message 
      }, { status: 500 });
    }

    // Update conversation's updated_at timestamp
    const { error: updateError } = await supabaseAdmin
      .from('conversations')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', supabaseConversationId);

    if (updateError) {
      console.error('Error updating conversation timestamp:', updateError);
    }

    return NextResponse.json({
      success: true,
      messageId: newMessage.id,
    });

  } catch (error: unknown) {
    console.error('Error in messages POST API:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 