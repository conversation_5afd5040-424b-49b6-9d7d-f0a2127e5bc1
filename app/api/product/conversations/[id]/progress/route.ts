import { NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: conversationId } = await params;

  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Verify conversation ownership
    const { data: conversation, error: convError } = await supabase
      .from('conversations')
      .select('id, processing_status')
      .eq('id', conversationId)
      .eq('user_id', user.id)
      .single();

    if (convError || !conversation) {
      return new Response('Conversation not found', { status: 404 });
    }

    // Create SSE stream
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      start(controller) {
        let intervalId: NodeJS.Timeout;
        let isActive = true;

        const sendUpdate = async () => {
          if (!isActive) return;

          try {
            const { data: currentConv } = await supabase
              .from('conversations')
              .select('processing_status, processing_progress, current_step_text, processing_error, title')
              .eq('id', conversationId)
              .single();

            if (currentConv) {
              const progress = currentConv.processing_progress || {};
              const progressData = {
                conversationId,
                status: currentConv.processing_status,
                title: currentConv.title, // Include conversation title for real-time updates
                progress: {
                  currentStep: progress.currentStep || currentConv.processing_status,
                  transcriptProgress: progress.transcriptProgress || 0,
                  optimizationProgress: progress.optimizationProgress || 0,
                  summaryProgress: progress.summaryProgress || 0,
                  transcriptLength: progress.transcriptLength || 0,
                  currentStepText: currentConv.current_step_text || progress.currentStepText || 'Processing...',
                  partialSummary: progress.partialSummary || null // Include streaming summary content
                },
                error: currentConv.processing_error,
                timestamp: new Date().toISOString()
              };

              controller.enqueue(encoder.encode(`data: ${JSON.stringify(progressData)}\n\n`));

              // Close stream if processing is complete or failed
              if (currentConv.processing_status === 'completed' || currentConv.processing_status === 'failed') {
                clearInterval(intervalId);
                controller.enqueue(encoder.encode('data: [DONE]\n\n'));
                controller.close();
                isActive = false;
              }
            }
          } catch (error) {
            console.error('Error sending progress update:', error);
            controller.enqueue(encoder.encode(`data: ${JSON.stringify({ error: 'Failed to get progress' })}\n\n`));
          }
        };

        // Send initial update
        sendUpdate();

        // Send updates every 1 second
        intervalId = setInterval(sendUpdate, 1000);

        // Cleanup on close
        request.signal?.addEventListener('abort', () => {
          clearInterval(intervalId);
          controller.close();
          isActive = false;
        });

        // Timeout after 10 minutes
        setTimeout(() => {
          if (isActive) {
            clearInterval(intervalId);
            controller.enqueue(encoder.encode('data: [TIMEOUT]\n\n'));
            controller.close();
            isActive = false;
          }
        }, 10 * 60 * 1000); // 10 minutes
      },

      cancel() {
        // Stream was cancelled
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      }
    });

  } catch (error) {
    console.error('Error setting up progress stream:', error);
    return new Response('Internal server error', { status: 500 });
  }
} 