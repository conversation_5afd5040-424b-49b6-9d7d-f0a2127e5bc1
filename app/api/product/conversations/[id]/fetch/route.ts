import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: conversationId } = await params;

  console.log('Fetch API called with conversation ID:', conversationId);

  if (!conversationId) {
    return NextResponse.json(
      { error: 'Conversation ID is required' },
      { status: 400 }
    );
  }

  try {
    const supabase = await createClient();

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Find the conversation by either Supabase ID or Dify conversation ID
    console.log('Looking for conversation with ID:', conversationId);
    const { data: conversation, error: conversationError } = await supabase
      .from('conversations')
      .select(`
        id, 
        youtube_video_id, 
        title, 
        dify_transcript_id,
        dify_summary_id,
        transcript
      `)
      .or(`id.eq.${conversationId},dify_summary_id.eq.${conversationId}`)
      .eq('user_id', user.id)
      .single();

    if (conversationError) {
      console.error('Error fetching conversation:', conversationError);
      return NextResponse.json(
        { error: 'Failed to fetch conversation details' },
        { status: 500 }
      );
    }

    const supabaseConversationId = conversation.id;
    console.log('Found conversation with Supabase ID:', supabaseConversationId);

    // Fetch messages for the conversation using the actual Supabase ID
    const { data: messages, error: messagesError } = await supabase
      .from('messages')
      .select('*')
      .eq('conversation_id', supabaseConversationId)
      .eq('user_id', user.id)
      .order('created_at', { ascending: true });

    if (messagesError) {
      console.error('Error fetching messages:', messagesError);
      return NextResponse.json(
        { error: 'Failed to fetch messages' },
        { status: 500 }
      );
    }

    // Fetch summary content for the conversation using the actual Supabase ID
    const { data: contentData, error: contentError } = await supabase
      .from('content')
      .select('content')
      .eq('conversation_id', supabaseConversationId)
      .single();

    if (contentError && contentError.code !== 'PGRST116') { // PGRST116 is "not found"
      console.error('Error fetching content:', contentError);
      return NextResponse.json(
        { error: 'Failed to fetch summary content' },
        { status: 500 }
      );
    }

    // Extract the content field
    let summaryContent = null;
    if (contentData?.content) {
      // Check if content is already an object or needs to be parsed
      if (typeof contentData.content === 'object') {
        // If it's already an object, access the content field directly
        summaryContent = contentData.content.content || null;
      } else if (typeof contentData.content === 'string') {
        try {
          // If it's a string, try to parse it as JSON
          const parsedContent = JSON.parse(contentData.content);
          summaryContent = parsedContent.content || parsedContent.Content || null;
        } catch (parseError) {
          console.warn('Failed to parse content JSON:', parseError);
          // If parsing fails, use the raw content string
          summaryContent = contentData.content;
        }
      }
    }

    // Extract video metadata from the combined transcript structure
    let videoMetadata = null;
    if (conversation.transcript && conversation.transcript.metadata) {
      videoMetadata = conversation.transcript.metadata;
      console.log('Found video metadata in transcript');
    }

    return NextResponse.json({
      success: true,
      data: {
        messages: messages || [],
        summary: summaryContent,
        conversation: {
          youtube_video_id: conversation.youtube_video_id,
          name: conversation.title,
          video_metadata: videoMetadata || null
        }
      }
    });

  } catch (error) {
    console.error('Error in fetch conversation API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 