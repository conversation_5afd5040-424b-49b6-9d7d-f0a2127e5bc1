import { NextResponse } from 'next/server';
import axios from 'axios';
import { createClient } from '@/lib/supabase/server';
import { cancellationManager } from '@/lib/background-processing/cancellationManager';

const DIFY_API_KEY = process.env.DIFY_API_KEY_SUMMARY; // Assuming you use the same key
const DIFY_BASE_URL = process.env.DIFY_BASE_URL || 'http://localhost/v1'; // Use env variable

export async function DELETE(
  request: Request, // Keep request param even if unused for Next.js convention
  { params }: { params: { id: string } }
) {
  const idToDelete = params.id;
  
  if (!idToDelete) {
    return NextResponse.json({ 
      message: 'Conversation ID is required in the URL path' 
    }, { status: 400 });
  }
  
  try {
    const supabase = await createClient();
    const {
      data: { user }
    } = await supabase.auth.getUser();

    if (!user) {
        return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = `user-${user.id}`; 
    
    if (!DIFY_API_KEY) {
        console.error('DIFY_API_KEY is not set');
        return NextResponse.json({ message: 'Server configuration error: API key missing' }, { status: 500 });
    }
    
    // Find the conversation in Supabase by either its own ID or the Dify conversation ID
    const { data: conversation, error: fetchError } = await supabase
      .from('conversations')
      .select('id, dify_summary_id, processing_status')
      .or(`id.eq.${idToDelete},dify_summary_id.eq.${idToDelete}`)
      .eq('user_id', user.id)
      .single();

    if (fetchError || !conversation) {
      console.error(`Conversation not found for id: ${idToDelete} for user ${user.id}`, fetchError);
      return NextResponse.json({ message: 'Conversation not found' }, { status: 404 });
    }

    const supabaseConversationId = conversation.id;
    const difyConversationId = conversation.dify_summary_id;
    const processingStatus = conversation.processing_status;

    console.log(`Attempting to delete conversation, Supabase ID: ${supabaseConversationId}, Dify ID: ${difyConversationId}, Processing Status: ${processingStatus}`);
    
    // Cancel any ongoing processing for this conversation
    if (processingStatus && ['pending', 'fetching', 'optimizing', 'summarizing'].includes(processingStatus)) {
      console.log(`Cancelling processing for conversation ${supabaseConversationId} (status: ${processingStatus})`);
      cancellationManager.cancelConversation(supabaseConversationId);
    }

    // 1. Delete associated content from public.content
    const { error: contentDeleteError } = await supabase
      .from('content')
      .delete()
      .eq('conversation_id', supabaseConversationId);

    if (contentDeleteError) {
      console.error(`Error deleting content for conversation ${supabaseConversationId}:`, contentDeleteError);
      return NextResponse.json({ message: 'Error deleting associated content', error: contentDeleteError.message }, { status: 500 });
    }
    console.log(`Successfully deleted content for conversation ${supabaseConversationId}`);

    // 2. Delete the conversation from public.conversations
    const { error: conversationDeleteError } = await supabase
      .from('conversations')
      .delete()
      .eq('id', supabaseConversationId);

    if (conversationDeleteError) {
      console.error(`Error deleting conversation ${supabaseConversationId} from Supabase:`, conversationDeleteError);
      return NextResponse.json({ message: 'Error deleting conversation from database', error: conversationDeleteError.message }, { status: 500 });
    }
    console.log(`Successfully deleted conversation ${supabaseConversationId} from Supabase`);

    // 3. Delete from Dify if a Dify ID exists
    if (difyConversationId) {
        try {
            console.log(`Attempting to delete Dify conversation ${difyConversationId}`);
            const response = await axios.delete(
                `${DIFY_BASE_URL}/conversations/${difyConversationId}`,
                {
                    headers: {
                        'Authorization': `Bearer ${DIFY_API_KEY}`,
                        'Content-Type': 'application/json',
                    },
                    data: { 
                        user: userId 
                    } 
                }
            );

            // Dify might return 204 No Content on success, which is valid.
            if (response.status === 204 || response.data?.result === 'success') {
                console.log(`Successfully deleted Dify conversation ${difyConversationId} (status: ${response.status})`);
            } else {
                // Log a warning if Dify returns an unexpected success status/data
                console.warn(`Dify delete request for ${difyConversationId} returned status ${response.status} but with unexpected data:`, response.data);
            }
        } catch (difyError: unknown) {
            // Log the error from Dify but do not fail the overall request
            if (axios.isAxiosError(difyError)) {
                console.warn(`Failed to delete conversation ${difyConversationId} from Dify. The conversation is already deleted from our database.`, {
                    message: difyError.message,
                    code: difyError.code,
                    status: difyError.response?.status,
                    data: difyError.response?.data,
                });
            } else {
                const error = difyError as Error;
                console.warn(`Failed to delete conversation ${difyConversationId} from Dify. The conversation is already deleted from our database.`, {
                    message: error.message,
                });
            }
        }
    } else {
        console.log(`No Dify conversation ID found for Supabase conversation ${supabaseConversationId}, skipping Dify deletion.`);
    }

    return NextResponse.json({ result: 'success' }, { status: 200 });
    
  } catch (error: unknown) {
    console.error(`Error during the primary deletion process for conversation ${idToDelete}:`, error);
    
    let statusCode = 500;
    let errorMessage = 'Internal Server Error';
    
    // Handle Axios errors
    if (axios.isAxiosError(error)) {
      console.error('Axios error details:', {
        message: error.message,
        code: error.code,
        status: error.response?.status,
        data: error.response?.data,
      });
      statusCode = error.response?.status || 500;
      errorMessage = error.response?.data?.message || error.message;
    } else {
      // Handle other types of errors
      const err = error as Error;
      errorMessage = err.message;
    }
    
    return NextResponse.json({ 
      message: 'Error deleting conversation', 
      error: errorMessage 
    }, { status: statusCode });
  }
}