import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: conversationId } = await params;

  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get conversation processing status
    const { data: conversation, error } = await supabase
      .from('conversations')
      .select(`
        id,
        title,
        processing_status,
        processing_progress,
        current_step_text,
        processing_error,
        processing_started_at,
        processing_completed_at
      `)
      .eq('id', conversationId)
      .eq('user_id', user.id)
      .single();

    if (error || !conversation) {
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
    }

    const progress = conversation.processing_progress || {};
    
    return NextResponse.json({
      conversationId,
      status: conversation.processing_status || 'idle',
      title: conversation.title, // Include title for real-time updates
      isProcessing: ['pending', 'processing', 'fetching', 'optimizing', 'summarizing'].includes(conversation.processing_status),
      progress: {
        currentStep: progress.currentStep || conversation.processing_status || 'idle',
        transcriptProgress: progress.transcriptProgress || 0,
        optimizationProgress: progress.optimizationProgress || 0,
        summaryProgress: progress.summaryProgress || 0,
        transcriptLength: progress.transcriptLength || 0,
        currentStepText: conversation.current_step_text || progress.currentStepText || null
      },
      error: conversation.processing_error,
      timestamps: {
        started: conversation.processing_started_at,
        completed: conversation.processing_completed_at
      }
    });

  } catch (error) {
    console.error('Error getting conversation status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 