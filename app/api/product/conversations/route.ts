import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    // Get user session
    const supabase = await createClient();
    const {
      data: { user }
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' }, 
        { status: 401 }
      );
    }
    
    // Fetch conversations from Supabase
    const { data: conversations, error } = await supabase
      .from('conversations')
      .select('id, title, created_at, updated_at, is_pinned, dify_summary_id, processing_status, youtube_video_id')
      .eq('user_id', user.id)
      .order('updated_at', { ascending: false })
      .limit(20);

    if (error) {
      console.error('Error fetching conversations:', error);
      return NextResponse.json(
        { message: 'Error fetching conversations', error: error.message }, 
        { status: 500 }
      );
    }

    // Transform the data to match the expected format (similar to Dify response)
    const transformedData = {
      data: conversations?.map(conv => ({
        id: conv.dify_summary_id || conv.id, // Use dify_summary_id if available, fallback to id
        name: conv.title || 'Processing…',
        created_at: Math.floor(new Date(conv.created_at).getTime() / 1000),
        updated_at: Math.floor(new Date(conv.updated_at).getTime() / 1000),
        processing_status: conv.processing_status,
        youtube_video_id: conv.youtube_video_id,
        supabase_id: conv.id, // Include the Supabase ID for internal reference
      })) || []
    };

    return NextResponse.json(transformedData);
    
  } catch (error: unknown) {
    console.error('Error fetching conversations:', error);
    
    const err = error as Error;
    return NextResponse.json(
      { message: 'Error fetching conversations', error: err.message }, 
      { status: 500 }
    );
  }
}

export async function POST() {
  try {
    // Get user session
    const supabase = await createClient();
    const {
      data: { user }
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { message: 'Unauthorized' }, 
        { status: 401 }
      );
    }
    
    console.log('Creating new conversation for user:', user.id);
    
    // Create a new conversation in Supabase
    const { data: newConversation, error } = await supabase
      .from('conversations')
      .insert({
        user_id: user.id,
        title: 'Processing…',
        is_pinned: false
      })
      .select('id, title, created_at, updated_at')
      .single();

    if (error) {
      console.error('Error creating conversation:', error);
      return NextResponse.json(
        { message: 'Error creating conversation', error: error.message }, 
        { status: 500 }
      );
    }
    
    console.log('Successfully created conversation:', newConversation);
    return NextResponse.json({ 
      conversation_id: newConversation.id,
      title: newConversation.title,
      created_at: newConversation.created_at,
      updated_at: newConversation.updated_at
    }, { status: 201 });
    
  } catch (error: unknown) {
    console.error('Error creating conversation:', error);
    
    const err = error as Error;
    return NextResponse.json({ 
      message: 'Error creating conversation', 
      error: err.message 
    }, { status: 500 });
  }
}