import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export const dynamic = 'force-dynamic';

export async function POST(req: NextRequest) {
  console.log('Starting fetch_transcript_both handler');
  
  try {
    const body = await req.json();
    
    const { conversationId } = body;
    
    if (!conversationId) {
      return NextResponse.json({ 
        success: false, 
        error: 'Missing required parameter: conversationId' 
      }, { status: 400 });
    }

    const supabase = await createClient();
    
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ 
        success: false, 
        error: 'Authentication required' 
      }, { status: 401 });
    }

    // Fetch the conversation with both transcript fields
    // Handle both Supabase ID and Dify conversation ID
    const { data: conversation, error: conversationError } = await supabase
      .from('conversations')
      .select('transcript, optimized_transcript')
      .or(`id.eq.${conversationId},dify_summary_id.eq.${conversationId}`)
      .eq('user_id', user.id)
      .single();

    if (conversationError || !conversation) {
      return NextResponse.json({ 
        success: false, 
        error: `Conversation ${conversationId} not found` 
      }, { status: 404 });
    }

    // Extract transcript content from the combined structure
    let rawTranscript = null;
    if (conversation.transcript) {
      // Check if it's the new combined structure
      if (conversation.transcript.transcript && conversation.transcript.transcript.content) {
        rawTranscript = conversation.transcript.transcript;
      } else {
        // Fallback for old structure
        rawTranscript = conversation.transcript;
      }
    }

    const optimizedTranscript = conversation.optimized_transcript;
    const hasOptimized = !!optimizedTranscript;

    return NextResponse.json({
      success: true,
      rawTranscript,
      optimizedTranscript,
      hasOptimized
    });

  } catch (error) {
    console.error('Error in fetch_transcript_both:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error' 
    }, { status: 500 });
  }
} 