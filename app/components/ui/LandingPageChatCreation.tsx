'use client';

import { useState, useCallback } from 'react';
import { Container, Paper, Stack, Title, Text, Button, Group } from '@mantine/core';
import { useRouter } from 'next/navigation';
import NewChatInput from '@/app/product/components/ui/NewChatInput';
import ThreeStepProgress from '@/app/product/components/ui/ThreeStepProgress';

interface LandingPageChatCreationProps {
  onClose?: () => void;
  showTitle?: boolean;
  containerSize?: string;
}

export function LandingPageChatCreation({ 
  onClose, 
  showTitle = true, 
  containerSize = "sm" 
}: LandingPageChatCreationProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [currentTitle, setCurrentTitle] = useState<string | null>(null);
  const [currentVideoUrl, setCurrentVideoUrl] = useState<string | null>(null);
  const [showProgress, setShowProgress] = useState(false);
  const router = useRouter();

  const handleConversationCreated = useCallback((
    conversationId: string,
    title: string,
    summary: string | null,
    videoUrl?: string
  ) => {
    setCurrentConversationId(conversationId);
    setCurrentTitle(title);
    setCurrentVideoUrl(videoUrl || null);
    setShowProgress(true);
    setIsCreating(false);
  }, []);

  const handleProgressCompleted = useCallback((conversationId: string) => {
    // Navigate to the conversation page when processing is complete
    router.push(`/product/${conversationId}`);
  }, [router]);

  const handleProgressFailed = useCallback((conversationId: string, error: string) => {
    console.error('Processing failed:', error);
    // Reset state to allow retry
    setShowProgress(false);
    setCurrentConversationId(null);
    setCurrentTitle(null);
    setCurrentVideoUrl(null);
  }, []);

  const handleCancel = useCallback(() => {
    setShowProgress(false);
    setCurrentConversationId(null);
    setCurrentTitle(null);
    setCurrentVideoUrl(null);
    setIsCreating(false);
    if (onClose) {
      onClose();
    }
  }, [onClose]);

  return (
    <Container size={containerSize} px="md">
      <Paper shadow="lg" radius="md" p="xl" withBorder>
        <Stack gap="lg">
          {showTitle && (
            <Stack gap="xs" ta="center">
              <Title order={2}>Try it now</Title>
              <Text c="dimmed">
                Paste any YouTube URL to see how we transform video content into useful text
              </Text>
            </Stack>
          )}

          {!showProgress ? (
            <Stack gap="md">
              <NewChatInput
                onConversationCreated={handleConversationCreated}
                useBackground={true}
                variant="main"
              />
              
              {onClose && (
                <Group justify="center">
                  <Button variant="subtle" onClick={onClose}>
                    Cancel
                  </Button>
                </Group>
              )}
            </Stack>
          ) : (
            <Stack gap="md">
              <Stack gap="xs" ta="center">
                <Title order={3} size="h4">
                  {currentTitle || 'Processing your video...'}
                </Title>
                {currentVideoUrl && (
                  <Text size="sm" c="dimmed">
                    {currentVideoUrl}
                  </Text>
                )}
              </Stack>

              <ThreeStepProgress
                conversationId={currentConversationId || undefined}
                onCompleted={handleProgressCompleted}
                onFailed={handleProgressFailed}
              />

              <Group justify="center">
                <Button variant="subtle" onClick={handleCancel}>
                  Cancel
                </Button>
              </Group>
            </Stack>
          )}
        </Stack>
      </Paper>
    </Container>
  );
}
