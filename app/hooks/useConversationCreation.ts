'use client';

import { useState, useCallback } from 'react';
import { useUserData } from '@/contexts/UserDataContext';

interface ConversationCreationResult {
  conversationId: string;
  title: string;
  videoUrl: string;
}

interface ConversationCreationCallbacks {
  onSuccess?: (result: ConversationCreationResult) => void;
  onError?: (error: string) => void;
}

export function useConversationCreation() {
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useUserData();

  const isValidYouTubeUrl = useCallback((url: string): boolean => {
    if (!url) return false;
    const regExp = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/(watch\?v=|embed\/|v\/)?([a-zA-Z0-9_-]{11})((\?|&)\S*)?$/;
    return regExp.test(url.trim());
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const createConversation = useCallback(async (
    youtubeUrl: string, 
    callbacks?: ConversationCreationCallbacks
  ): Promise<ConversationCreationResult | null> => {
    const trimmedUrl = youtubeUrl.trim();
    
    // Clear any previous errors
    setError(null);

    // Validate user authentication
    if (!user?.id) {
      const errorMessage = 'User not authenticated';
      setError(errorMessage);
      callbacks?.onError?.(errorMessage);
      return null;
    }

    // Validate YouTube URL
    if (!isValidYouTubeUrl(trimmedUrl)) {
      const errorMessage = 'Please enter a valid YouTube URL.';
      setError(errorMessage);
      callbacks?.onError?.(errorMessage);
      return null;
    }
    
    setIsCreating(true);
    
    try {
      // Step 1: Create a new conversation
      const createResponse = await fetch('/api/product/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      const createResult = await createResponse.json();
      
      if (!createResponse.ok) {
        throw new Error(createResult.error || 'Failed to create conversation');
      }
      
      const conversationId = createResult.conversation_id;
      
      // Step 2: Start processing the YouTube URL
      const processResponse = await fetch(`/api/product/conversations/${conversationId}/start-processing`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ youtubeUrl: trimmedUrl })
      });
      
      const processResult = await processResponse.json();
      
      if (!processResponse.ok) {
        throw new Error(processResult.error || 'Failed to start processing');
      }
      
      const result: ConversationCreationResult = {
        conversationId,
        title: processResult.title || 'Processing...',
        videoUrl: trimmedUrl
      };
      
      callbacks?.onSuccess?.(result);
      return result;
      
    } catch (error: unknown) {
      console.error('Error creating conversation:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create conversation';
      setError(errorMessage);
      callbacks?.onError?.(errorMessage);
      return null;
    } finally {
      setIsCreating(false);
    }
  }, [user, isValidYouTubeUrl]);

  const createConversationBackground = useCallback(async (
    youtubeUrl: string,
    callbacks?: ConversationCreationCallbacks
  ): Promise<ConversationCreationResult | null> => {
    const trimmedUrl = youtubeUrl.trim();
    
    // Clear any previous errors
    setError(null);

    // Validate user authentication
    if (!user?.id) {
      const errorMessage = 'User not authenticated';
      setError(errorMessage);
      callbacks?.onError?.(errorMessage);
      return null;
    }

    // Validate YouTube URL
    if (!isValidYouTubeUrl(trimmedUrl)) {
      const errorMessage = 'Please enter a valid YouTube URL.';
      setError(errorMessage);
      callbacks?.onError?.(errorMessage);
      return null;
    }
    
    setIsCreating(true);
    
    try {
      // Step 1: Create conversation
      const createResponse = await fetch('/api/product/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      const createResult = await createResponse.json();
      
      if (!createResponse.ok) {
        throw new Error(createResult.error || 'Failed to create conversation');
      }
      
      const conversationId = createResult.conversation_id;
      
      // Step 2: Start background processing
      const processResponse = await fetch(`/api/product/conversations/${conversationId}/start-processing`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ youtubeUrl: trimmedUrl })
      });
      
      const processResult = await processResponse.json();

      if (!processResponse.ok) {
        throw new Error(processResult.error || 'Failed to start background processing');
      }
      
      const result: ConversationCreationResult = {
        conversationId,
        title: 'Processing...',
        videoUrl: trimmedUrl
      };
      
      callbacks?.onSuccess?.(result);
      return result;
      
    } catch (error: unknown) {
      console.error('Error starting background conversation creation:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create conversation';
      setError(errorMessage);
      callbacks?.onError?.(errorMessage);
      return null;
    } finally {
      setIsCreating(false);
    }
  }, [user, isValidYouTubeUrl]);

  return {
    createConversation,
    createConversationBackground,
    isCreating,
    error,
    clearError,
    isValidYouTubeUrl
  };
}
