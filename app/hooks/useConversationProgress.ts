'use client';

import { useState, useCallback, useRef } from 'react';
import { ConversationProgress } from '@/app/product/components/ui/ThreeStepProgress';

interface ConversationProgressCallbacks {
  onProgressUpdate?: (progress: ConversationProgress) => void;
  onTitleUpdate?: (conversationId: string, oldTitle: string, newTitle: string) => void;
  onTranscriptFetched?: (conversationId: string) => void;
  onTranscriptOptimized?: (conversationId: string) => void;
  onStepCompleted?: (conversationId: string, fromStep: string, toStep: string) => void;
  onCompleted?: (conversationId: string) => void;
  onFailed?: (conversationId: string, error: string) => void;
  onError?: (error: string) => void;
}

export function useConversationProgress() {
  const [progress, setProgress] = useState<ConversationProgress | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Store active subscriptions to prevent memory leaks
  const activeSubscriptions = useRef<Map<string, () => void>>(new Map());

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const clearProgress = useCallback(() => {
    setProgress(null);
  }, []);

  const subscribeToProgress = useCallback((
    conversationId: string, 
    callbacks?: ConversationProgressCallbacks
  ) => {
    console.log('Subscribing to progress for conversation:', conversationId);
    
    // Clean up any existing subscription for this conversation
    const existingCleanup = activeSubscriptions.current.get(conversationId);
    if (existingCleanup) {
      existingCleanup();
    }
    
    setIsTracking(true);
    setError(null);
    
    let lastStatus = '';
    const eventSource = new EventSource(`/api/product/conversations/${conversationId}/progress`);
    
    eventSource.onmessage = (event) => {
      if (event.data === '[DONE]' || event.data === '[TIMEOUT]') {
        eventSource.close();
        setIsTracking(false);
        return;
      }
      
      try {
        const progressData = JSON.parse(event.data);

        if (progressData.error) {
          console.error('Progress error:', progressData.error);
          setError(progressData.error);
          callbacks?.onError?.(progressData.error);
          eventSource.close();
          setIsTracking(false);
          return;
        }
        
        // Update progress state
        if (progressData.progress) {
          setProgress(progressData.progress);
          callbacks?.onProgressUpdate?.(progressData.progress);
        }
        
        // Handle real-time title updates
        if (progressData.title && progressData.title !== 'Processing…' && progressData.title !== 'Processing...') {
          console.log('Title updated in real-time:', progressData.title);
          callbacks?.onTitleUpdate?.(conversationId, 'Processing...', progressData.title);
        }
        
        // Check for transcript fetch completion  
        if (progressData.progress?.currentStepText === 'Video data fetched successfully') {
          console.log('Transcript fetched, triggering callback for conversation:', conversationId);
          callbacks?.onTranscriptFetched?.(conversationId);
        }
        
        // Check for transcript optimization completion
        if (progressData.progress?.currentStepText === 'Transcript optimization completed') {
          console.log('Transcript optimization completed, triggering callback for conversation:', conversationId);
          callbacks?.onTranscriptOptimized?.(conversationId);
        }
        
        // Check for step completion by detecting status transitions
        const currentStatus = progressData.status;
        if (lastStatus && lastStatus !== currentStatus) {
          console.log(`Step completed: ${lastStatus} -> ${currentStatus} for conversation:`, conversationId);
          callbacks?.onStepCompleted?.(conversationId, lastStatus, currentStatus);
        }
        lastStatus = currentStatus;
        
        // Handle completion
        if (progressData.status === 'completed') {
          console.log('Processing completed for conversation:', conversationId);
          eventSource.close();
          setIsTracking(false);
          callbacks?.onCompleted?.(conversationId);
          
          // Clear progress after a short delay
          setTimeout(() => {
            setProgress(null);
          }, 2000);
        } else if (progressData.status === 'failed') {
          console.error('Processing failed for conversation:', conversationId);
          eventSource.close();
          setIsTracking(false);
          
          const errorMessage = progressData.error || 'Processing failed';
          setError(errorMessage);
          callbacks?.onFailed?.(conversationId, errorMessage);
          setProgress(null);
        }
        
      } catch (e) {
        console.error('Failed to parse progress data:', {
          rawData: event.data,
          dataType: typeof event.data,
          dataLength: event.data?.length,
          error: e
        });

        // If we can't parse the data, it might be a malformed error message
        // Try to extract any useful information and continue
        if (event.data && typeof event.data === 'string') {
          // Check if it looks like an error message
          if (event.data.includes('error') || event.data.includes('Error') || event.data.includes('failed')) {
            console.warn('Detected error in malformed SSE data, closing connection');
            setError('Processing encountered an error');
            callbacks?.onError?.(event.data);
            eventSource.close();
            setIsTracking(false);
          }
        }
      }
    };
    
    eventSource.onerror = (error) => {
      console.warn('SSE connection error, falling back to polling:', error);
      eventSource.close();
      
      // Fallback to polling
      startPolling(conversationId, callbacks);
    };
    
    // Store cleanup function
    const cleanup = () => {
      eventSource.close();
      setIsTracking(false);
      activeSubscriptions.current.delete(conversationId);
    };
    
    activeSubscriptions.current.set(conversationId, cleanup);
    return cleanup;
  }, []);

  const startPolling = useCallback((
    conversationId: string, 
    callbacks?: ConversationProgressCallbacks
  ) => {
    let lastPollStatus = '';
    
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/product/conversations/${conversationId}/status`);
        const statusData = await response.json();
        
        if (!response.ok) {
          throw new Error(statusData.error || 'Failed to get status');
        }
        
        if (statusData.progress) {
          setProgress(statusData.progress);
          callbacks?.onProgressUpdate?.(statusData.progress);
        }
        
        // Handle real-time title updates
        if (statusData.title && statusData.title !== 'Processing…' && statusData.title !== 'Processing...') {
          console.log('Title updated in real-time (polling):', statusData.title);
          callbacks?.onTitleUpdate?.(conversationId, 'Processing...', statusData.title);
        }
        
        // Check for transcript fetch completion
        if (statusData.progress?.currentStepText === 'Video data fetched successfully') {
          console.log('Transcript fetched (polling), triggering callback for conversation:', conversationId);
          callbacks?.onTranscriptFetched?.(conversationId);
        }
        
        // Check for transcript optimization completion
        if (statusData.progress?.currentStepText === 'Transcript optimization completed') {
          console.log('Transcript optimization completed (polling), triggering callback for conversation:', conversationId);
          callbacks?.onTranscriptOptimized?.(conversationId);
        }
        
        // Check for step completion
        const currentStatus = statusData.status;
        if (lastPollStatus && lastPollStatus !== currentStatus) {
          console.log(`Step completed (polling): ${lastPollStatus} -> ${currentStatus} for conversation:`, conversationId);
          callbacks?.onStepCompleted?.(conversationId, lastPollStatus, currentStatus);
        }
        lastPollStatus = currentStatus;
        
        if (statusData.status === 'completed' || statusData.status === 'failed') {
          clearInterval(pollInterval);
          setIsTracking(false);
          
          if (statusData.status === 'completed') {
            callbacks?.onCompleted?.(conversationId);
            setTimeout(() => setProgress(null), 2000);
          } else {
            const errorMessage = statusData.error || 'Processing failed';
            setError(errorMessage);
            callbacks?.onFailed?.(conversationId, errorMessage);
            setProgress(null);
          }
        }
        
      } catch (error) {
        console.error('Error polling for progress:', error);
        clearInterval(pollInterval);
        setIsTracking(false);
      }
    }, 2000);
    
    // Store cleanup function
    const cleanup = () => {
      clearInterval(pollInterval);
      setIsTracking(false);
      activeSubscriptions.current.delete(conversationId);
    };
    
    activeSubscriptions.current.set(conversationId, cleanup);
    return cleanup;
  }, []);

  const checkExistingProcessing = useCallback(async (
    conversationId: string,
    callbacks?: ConversationProgressCallbacks
  ) => {
    try {
      const response = await fetch(`/api/product/conversations/${conversationId}/status`);
      const statusData = await response.json();
      
      if (response.ok && statusData.isProcessing) {
        console.log('Found existing processing for conversation:', conversationId);
        return subscribeToProgress(conversationId, callbacks);
      }
      
      return null;
    } catch (error) {
      console.error('Error checking existing processing:', error);
      return null;
    }
  }, [subscribeToProgress]);

  const stopTracking = useCallback((conversationId?: string) => {
    if (conversationId) {
      const cleanup = activeSubscriptions.current.get(conversationId);
      if (cleanup) {
        cleanup();
      }
    } else {
      // Stop all tracking
      activeSubscriptions.current.forEach(cleanup => cleanup());
      activeSubscriptions.current.clear();
      setIsTracking(false);
      setProgress(null);
    }
  }, []);

  return {
    progress,
    isTracking,
    error,
    subscribeToProgress,
    checkExistingProcessing,
    stopTracking,
    clearError,
    clearProgress
  };
}
